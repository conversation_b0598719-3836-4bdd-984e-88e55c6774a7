const login = require('fca-unofficial');
const fs = require('fs-extra');
const moment = require('moment');
const logger = require('./utils/logger');

// Load cấu hình
const settings = require('./config/settings.json');

class MessengerBot {
    constructor() {
        this.api = null;
        this.botName = settings.bot.name;
        this.prefix = settings.bot.prefix;
        this.commands = new Map();
        this.setupCommands();
    }

    // Thiết lập các lệnh
    setupCommands() {
        this.commands.set('ping', this.pingCommand.bind(this));
        this.commands.set('help', this.helpCommand.bind(this));
        this.commands.set('time', this.timeCommand.bind(this));
        this.commands.set('info', this.infoCommand.bind(this));
    }

    // Đăng nhập bot
    async start() {
        try {
            logger.showBanner();
            logger.info('<PERSON>ang đăng nhập...');

            // Đ<PERSON>c appstate
            let appState;
            try {
                appState = JSON.parse(fs.readFileSync('./config/appstate.json', 'utf8'));
                if (!appState || appState.length === 0) {
                    throw new Error('AppState trống');
                }
            } catch (error) {
                logger.error('Không thể đọc appstate.json!');
                logger.warn('Hướng dẫn lấy appstate:');
                logger.info('1. Đăng nhập Facebook trên trình duyệt');
                logger.info('2. Mở Developer Tools (F12)');
                logger.info('3. Vào tab Application/Storage > Cookies');
                logger.info('4. Copy tất cả cookies và paste vào appstate.json');
                logger.info('5. Hoặc sử dụng extension "Get cookies.txt"');
                process.exit(1);
            }

            // Đăng nhập
            login({ appState }, (err, api) => {
                if (err) {
                    logger.error('Đăng nhập thất bại!');
                    logger.error(err.error || err);
                    
                    if (err.error === 'login-approval') {
                        logger.warn('Tài khoản cần xác thực 2FA!');
                    } else if (err.error === 'checkpoint') {
                        logger.warn('Tài khoản bị checkpoint! Hãy đăng nhập trình duyệt để xử lý.');
                    }
                    return;
                }

                this.api = api;
                this.setupBot();
            });

        } catch (error) {
            logger.error('Lỗi khởi động bot:', error.message);
        }
    }

    // Thiết lập bot sau khi đăng nhập thành công
    setupBot() {
        // Cấu hình API
        this.api.setOptions({
            listenEvents: true,
            logLevel: 'silent',
            selfListen: false,
            updatePresence: true
        });

        // Lấy thông tin bot
        this.api.getCurrentUserID((err, userID) => {
            if (err) {
                logger.error('Không thể lấy thông tin bot:', err);
                return;
            }

            this.api.getUserInfo(userID, (err, userInfo) => {
                if (err) {
                    logger.error('Không thể lấy thông tin user:', err);
                    return;
                }

                const botInfo = userInfo[userID];
                logger.showConnected(botInfo.name);
                
                // Bắt đầu lắng nghe tin nhắn
                this.startListening();
            });
        });
    }

    // Bắt đầu lắng nghe tin nhắn
    startListening() {
        this.api.listen((err, message) => {
            if (err) {
                logger.error('Lỗi khi lắng nghe tin nhắn:', err);
                return;
            }

            this.handleMessage(message);
        });
    }

    // Xử lý tin nhắn
    handleMessage(message) {
        if (message.type !== 'message' || !message.body) return;

        const { threadID, messageID, senderID, body } = message;
        
        // Log tin nhắn nếu được bật
        if (settings.bot.logMessages) {
            logger.debug(`Tin nhắn từ ${senderID}: ${body}`);
        }

        // Xử lý lệnh
        if (body.startsWith(this.prefix)) {
            this.handleCommand(threadID, messageID, senderID, body);
        } else if (settings.bot.autoReply) {
            // Phản hồi tự động
            this.handleAutoReply(threadID, messageID, body);
        }
    }

    // Xử lý lệnh
    handleCommand(threadID, messageID, senderID, body) {
        const args = body.slice(this.prefix.length).trim().split(' ');
        const commandName = args[0].toLowerCase();

        if (this.commands.has(commandName)) {
            try {
                this.commands.get(commandName)(threadID, messageID, senderID, args);
                logger.info(`Lệnh ${commandName} được thực hiện bởi ${senderID}`);
            } catch (error) {
                logger.error(`Lỗi khi thực hiện lệnh ${commandName}:`, error.message);
                this.sendMessage(threadID, '❌ Có lỗi xảy ra khi thực hiện lệnh!', messageID);
            }
        } else {
            this.sendMessage(threadID, `❌ Lệnh "${commandName}" không tồn tại. Gõ ${this.prefix}help để xem danh sách lệnh.`, messageID);
        }
    }

    // Phản hồi tự động
    handleAutoReply(threadID, messageID, body) {
        const lowerBody = body.toLowerCase();
        
        // Chào hỏi
        if (lowerBody.includes('hello') || lowerBody.includes('hi') || lowerBody.includes('chào')) {
            const greetings = settings.responses.greetings;
            const randomGreeting = greetings[Math.floor(Math.random() * greetings.length)];
            this.sendMessage(threadID, randomGreeting, messageID);
        }
        // Cảm ơn
        else if (lowerBody.includes('cảm ơn') || lowerBody.includes('thanks') || lowerBody.includes('thank you')) {
            const thanks = settings.responses.thanks;
            const randomThanks = thanks[Math.floor(Math.random() * thanks.length)];
            this.sendMessage(threadID, randomThanks, messageID);
        }
        // Tạm biệt
        else if (lowerBody.includes('bye') || lowerBody.includes('tạm biệt') || lowerBody.includes('goodbye')) {
            const goodbyes = settings.responses.goodbye;
            const randomGoodbye = goodbyes[Math.floor(Math.random() * goodbyes.length)];
            this.sendMessage(threadID, randomGoodbye, messageID);
        }
    }

    // Gửi tin nhắn
    sendMessage(threadID, message, messageID = null) {
        const options = messageID ? { replyToMessage: messageID } : {};
        this.api.sendMessage(message, threadID, options);
    }

    // === COMMANDS ===

    // Lệnh ping
    pingCommand(threadID, messageID) {
        this.sendMessage(threadID, '🏓 Pong! Bot đang hoạt động bình thường.', messageID);
    }

    // Lệnh help
    helpCommand(threadID, messageID) {
        let helpText = `📋 DANH SÁCH LỆNH\n\n`;
        
        for (const [cmd, info] of Object.entries(settings.commands)) {
            helpText += `${this.prefix}${cmd} - ${info.description}\n`;
        }
        
        helpText += `\n💡 Sử dụng: ${this.prefix}<tên_lệnh>`;
        this.sendMessage(threadID, helpText, messageID);
    }

    // Lệnh time
    timeCommand(threadID, messageID) {
        const now = moment().format('LLLL');
        this.sendMessage(threadID, `🕐 Thời gian hiện tại: ${now}`, messageID);
    }

    // Lệnh info
    infoCommand(threadID, messageID) {
        const info = `🤖 THÔNG TIN BOT
        
📛 Tên: ${this.botName}
⚡ Phiên bản: 1.0.0
🔧 Prefix: ${this.prefix}
📅 Khởi động: ${moment().format('DD/MM/YYYY HH:mm:ss')}
💻 Nền tảng: Node.js + FCA-Unofficial

🌟 Bot được tạo bởi DexBot Team`;

        this.sendMessage(threadID, info, messageID);
    }
}

// Khởi động bot
const bot = new MessengerBot();
bot.start();

// Xử lý thoát chương trình
process.on('SIGINT', () => {
    logger.warn('Đang thoát bot...');
    process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection:', reason);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});
