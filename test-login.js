const login = require('fca-unofficial');
const fs = require('fs-extra');

console.log('🔐 Test đăng nhập Facebook...\n');

// Thử đọc appstate trước
try {
    const appState = JSON.parse(fs.readFileSync('./config/appstate.json', 'utf8'));
    
    if (!appState || appState.length === 0) {
        console.log('❌ AppState trống hoặc không hợp lệ!');
        console.log('\n📋 Hướng dẫn lấy AppState:');
        console.log('1. Cài extension "Get cookies.txt LOCALLY" trên Chrome');
        console.log('2. Đăng nhập Facebook');
        console.log('3. Click extension > Export as JSON');
        console.log('4. Paste vào config/appstate.json');
        process.exit(1);
    }

    console.log('✅ Tìm thấy AppState, đang test đăng nhập...');
    
    login({ appState }, (err, api) => {
        if (err) {
            console.log('❌ Đăng nhập thất bại!');
            console.log('Lỗi:', err.error || err);
            
            if (err.error === 'checkpoint') {
                console.log('\n🚫 Tài khoản bị checkpoint!');
                console.log('Giải pháp: Đăng nhập Facebook trên trình duyệt để xử lý checkpoint');
            } else if (err.error === 'login-approval') {
                console.log('\n📱 Cần xác thực 2FA!');
                console.log('Giải pháp: Tắt 2FA hoặc lấy AppState sau khi xác thực');
            } else {
                console.log('\n🔄 AppState có thể đã hết hạn!');
                console.log('Giải pháp: Lấy AppState mới từ trình duyệt');
            }
            return;
        }

        console.log('🎉 Đăng nhập thành công!');
        
        // Lấy thông tin bot
        api.getCurrentUserID((err, userID) => {
            if (!err) {
                api.getUserInfo(userID, (err, userInfo) => {
                    if (!err) {
                        const botInfo = userInfo[userID];
                        console.log(`👤 Tên bot: ${botInfo.name}`);
                        console.log(`🆔 ID: ${userID}`);
                        console.log('\n✅ Bot sẵn sàng hoạt động!');
                        console.log('🚀 Chạy lệnh: npm start');
                    }
                    process.exit(0);
                });
            } else {
                console.log('✅ Đăng nhập OK, sẵn sàng chạy bot!');
                process.exit(0);
            }
        });
    });

} catch (error) {
    console.log('❌ Không thể đọc file appstate.json!');
    console.log('\n📋 Hướng dẫn tạo AppState:');
    console.log('1. Cài extension "Get cookies.txt LOCALLY" trên Chrome');
    console.log('2. Đăng nhập Facebook');
    console.log('3. Vào facebook.com');
    console.log('4. Click extension > Export as JSON');
    console.log('5. Paste toàn bộ nội dung vào config/appstate.json');
    console.log('\nVí dụ format AppState:');
    console.log('[{"key":"c_user","value":"123456789","domain":".facebook.com",...}]');
}
