module.exports = {
  "delta": {
    "attachments": [
      {
        "fbid": "1522004821162174",
        "id": "1522004821162174",
        "mercury": {
          "app_attribution": null,
          "attach_type": "share",
          "name": null,
          "url": null,
          "rel": null,
          "preview_url": null,
          "preview_width": null,
          "preview_height": null,
          "large_preview_url": null,
          "large_preview_width": null,
          "large_preview_height": null,
          "icon_type": null,
          "metadata": null,
          "thumbnail_url": null,
          "share": {
            "description": null,
            "media": {
              "animated_image": null,
              "animated_image_size": {
                "height": null,
                "width": null
              },
              "image": null,
              "image_size": {
                "height": null,
                "width": null
              },
              "duration": null,
              "playable": null,
              "source": null
            },
            "source": "Dimon - testing",
            "style_list": [
              "business_message_items", "fallback"
            ],
            "title": "search engines",
            "properties": null,
            "uri": null,
            "subattachments": [],
            "deduplication_key": "abcde",
            "action_links": [],
            "share_id": "1522004821162174",
            "target": {
              "call_to_actions": [],
              "items": [
                {
                  "id": "629934437209008",
                  "name": "search engines",
                  "desc": "",
                  "thumb_url": null,
                  "item_url": null,
                  "title": "search engines",
                  "text": "",
                  "source": null,
                  "metalines": {
                    "metaline_1": "click to get redirected",
                    "metaline_2": null,
                    "metaline_3": null
                  },
                  "location": 12314,
                  "category": 69,
                  "call_to_actions": [
                    {
                      "action_link": "http://l.facebook.com/l.php?u=http%3A%2F%2Fgoogle.com%2F&h=ATNziCq_-6I3ZPYwwLluFdCrWMEwLLKvokFlXdEdS4LD2Lzsv2cR2SJYffJcDYBfB092Xeq8oRdftJk4husEYVduH24RnlP3HvVQOkOrciXDs2M7TkWYyNLBelvJ2Fc-mw8pbGy5NslGf_fkZ_A",
                      "action_type": 2,
                      "id": "FFD=",
                      "title": "Google",
                      "link_target_ids": [629934437209008],
                      "is_mutable_by_server": false,
                      "should_show_user_confirmation": false,
                      "confirmation_title": null,
                      "confirmation_message": null,
                      "confirmation_continue_label": null,
                      "confirmation_cancel_label": null,
                      "payment_metadata": {
                        "total_price": null,
                        "payment_module_config": null
                      },
                      "is_disabled": false
                    }, {
                      "action_link": "http://l.facebook.com/l.php?u=http%3A%2F%2Fyahoo.com%2F&h=ATNIuTf7iDGP5xXTWOAdhaGhRFfDf4eS09t_G9CrR0MDiBKpqtCDzPf_9y5Bq7TXMgmo6RttztsgeO0ReSc0PDvJDTa1fLMMK2CjrpkqC91_m-yaMXfeQ4aI6MbhZrOPnK3YFnQP4XvRx3N1udE",
                      "action_type": 2,
                      "id": "CDE=",
                      "title": "Yahoo",
                      "link_target_ids": [629934437209008],
                      "is_mutable_by_server": false,
                      "should_show_user_confirmation": false,
                      "confirmation_title": null,
                      "confirmation_message": null,
                      "confirmation_continue_label": null,
                      "confirmation_cancel_label": null,
                      "payment_metadata": {
                        "total_price": null,
                        "payment_module_config": null
                      },
                      "is_disabled": false
                    }, {
                      "action_link": "http://l.facebook.com/l.php?u=http%3A%2F%2Fbing.com%2F&h=ATMoMijAt6Da6WWIQ679DhZyZizWdxAViWwyl-RjKobFUG_x8GmB8LD6pPa3KP5K1-QTL9vuaFwjqB0itaMFWk4VwQ9uh56JgnbFnAo4qM_CrQufgLeHwwCnWSCnZt8IzYT4y6YULLLFA5bL1H4",
                      "action_type": 2,
                      "id": "ABC=",
                      "title": "Bing",
                      "link_target_ids": [629934437209008],
                      "is_mutable_by_server": false,
                      "should_show_user_confirmation": false,
                      "confirmation_title": null,
                      "confirmation_message": null,
                      "confirmation_continue_label": null,
                      "confirmation_cancel_label": null,
                      "payment_metadata": {
                        "total_price": null,
                        "payment_module_config": null
                      },
                      "is_disabled": false
                    }
                  ]
                }
              ],
              "location": 132145,
              "category": 69,
              "message": "Aaa: search engines"
            }
          }
        },
        "otherUserFbIds": ["1521994257829897"],
        "titanType": 1
      }
    ],
    "messageMetadata": {
      "actorFbId": "1345",
      "messageId": "mid.12345:asdv",
      "offlineThreadingId": "1345v1345",
      "tags": ["source:messenger:commerce"],
      "threadKey": {
        "otherUserFbId": "13451345"
      },
      "timestamp": "1487078180265"
    }
  }
}
