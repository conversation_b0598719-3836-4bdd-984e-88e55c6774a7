const login = require('fca-unofficial');
const fs = require('fs-extra');
const readline = require('readline');
const logger = require('./utils/logger');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log(`
╔══════════════════════════════════════╗
║         APPSTATE GENERATOR           ║
║     Tạo AppState cho Messenger Bot   ║
╚══════════════════════════════════════╝
`);

console.log('⚠️  CẢNH BÁO: Chỉ sử dụng tài khoản phụ để tránh rủi ro!');
console.log('📧 Nhập thông tin đăng nhập Facebook:\n');

rl.question('Email/Phone: ', (email) => {
    rl.question('Password: ', (password) => {
        console.log('\n🔄 Đang đăng nhập...');
        
        login({ email, password }, (err, api) => {
            if (err) {
                console.error('❌ Đăng nhập thất bại!');
                
                switch (err.error) {
                    case 'login-approval':
                        console.log('📱 Cần xác thực 2FA. Kiểm tra điện thoại/email của bạn.');
                        rl.question('Nhập mã xác thực: ', (code) => {
                            login({ email, password, code }, handleLoginResult);
                        });
                        return;
                        
                    case 'checkpoint':
                        console.log('🚫 Tài khoản bị checkpoint. Hãy đăng nhập Facebook trên trình duyệt để xử lý.');
                        break;
                        
                    default:
                        console.log('❌ Lỗi:', err.error || err);
                }
                
                rl.close();
                return;
            }
            
            handleLoginResult(null, api);
        });
    });
});

function handleLoginResult(err, api) {
    if (err) {
        console.error('❌ Xác thực thất bại:', err.error || err);
        rl.close();
        return;
    }
    
    try {
        // Lấy AppState
        const appState = api.getAppState();
        
        // Lưu vào file
        fs.writeFileSync('./config/appstate.json', JSON.stringify(appState, null, 2));
        
        console.log('✅ Tạo AppState thành công!');
        console.log('📁 Đã lưu vào: config/appstate.json');
        console.log('🚀 Bây giờ bạn có thể chạy bot bằng: npm start');
        
        // Backup AppState
        const backupPath = `./config/appstate_backup_${Date.now()}.json`;
        fs.writeFileSync(backupPath, JSON.stringify(appState, null, 2));
        console.log(`💾 Backup tại: ${backupPath}`);
        
    } catch (error) {
        console.error('❌ Lỗi khi lưu AppState:', error.message);
    }
    
    rl.close();
    process.exit(0);
}

// Xử lý Ctrl+C
process.on('SIGINT', () => {
    console.log('\n👋 Đã hủy quá trình tạo AppState.');
    rl.close();
    process.exit(0);
});
