const fs = require('fs-extra');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log(`
╔══════════════════════════════════════╗
║       COOKIE TO APPSTATE TOOL       ║
║    Chuyển đổi Cookies thành AppState ║
╚══════════════════════════════════════╝
`);

console.log('📋 Hướng dẫn:');
console.log('1. Đ<PERSON>ng nhập Facebook trên Chrome');
console.log('2. F12 → Application → Cookies → facebook.com');
console.log('3. Nhập từng cookie theo yêu cầu dưới đây\n');

const cookies = {};
const requiredCookies = ['c_user', 'xs', 'fr', 'sb', 'datr'];

let currentIndex = 0;

function askForCookie() {
    if (currentIndex >= requiredCookies.length) {
        generateAppState();
        return;
    }

    const cookieName = requiredCookies[currentIndex];
    rl.question(`Nhập giá trị cookie "${cookieName}": `, (value) => {
        if (value.trim()) {
            cookies[cookieName] = value.trim();
            console.log(`✅ Đã lưu ${cookieName}`);
        } else {
            console.log(`⚠️ Bỏ qua ${cookieName} (có thể không cần thiết)`);
        }
        
        currentIndex++;
        askForCookie();
    });
}

function generateAppState() {
    console.log('\n🔄 Đang tạo AppState...');
    
    const appState = [];
    const now = new Date().toISOString();
    
    for (const [key, value] of Object.entries(cookies)) {
        appState.push({
            key: key,
            value: value,
            domain: ".facebook.com",
            path: "/",
            hostOnly: false,
            creation: now,
            lastAccessed: now,
            secure: true,
            httpOnly: key === 'xs' || key === 'fr'
        });
    }
    
    try {
        // Lưu AppState
        fs.writeFileSync('./config/appstate.json', JSON.stringify(appState, null, 2));
        console.log('✅ Tạo AppState thành công!');
        console.log('📁 Đã lưu vào: config/appstate.json');
        
        // Backup
        const backupPath = `./config/appstate_backup_${Date.now()}.json`;
        fs.writeFileSync(backupPath, JSON.stringify(appState, null, 2));
        console.log(`💾 Backup tại: ${backupPath}`);
        
        console.log('\n🧪 Test đăng nhập...');
        testLogin();
        
    } catch (error) {
        console.log('❌ Lỗi khi lưu AppState:', error.message);
        rl.close();
    }
}

function testLogin() {
    const login = require('fca-unofficial');
    
    try {
        const appState = JSON.parse(fs.readFileSync('./config/appstate.json', 'utf8'));
        
        login({ appState }, (err, api) => {
            if (err) {
                console.log('❌ Test đăng nhập thất bại!');
                console.log('Lỗi:', err.error || err);
                
                if (err.error === 'checkpoint') {
                    console.log('\n🚫 Tài khoản bị checkpoint!');
                    console.log('Giải pháp: Xử lý checkpoint trên trình duyệt trước');
                } else if (err.error === 'login-approval') {
                    console.log('\n📱 Cần xác thực 2FA!');
                    console.log('Giải pháp: Tắt 2FA hoặc lấy cookies sau khi xác thực');
                } else {
                    console.log('\n🔄 Cookies có thể không đúng hoặc đã hết hạn!');
                    console.log('Giải pháp: Lấy cookies mới từ trình duyệt');
                }
            } else {
                console.log('🎉 Test đăng nhập thành công!');
                console.log('🚀 Bây giờ bạn có thể chạy: npm start');
                
                // Lấy thông tin bot
                api.getCurrentUserID((err, userID) => {
                    if (!err) {
                        api.getUserInfo(userID, (err, userInfo) => {
                            if (!err) {
                                const botInfo = userInfo[userID];
                                console.log(`👤 Tên bot: ${botInfo.name}`);
                                console.log(`🆔 ID: ${userID}`);
                            }
                        });
                    }
                });
            }
            
            rl.close();
        });
        
    } catch (error) {
        console.log('❌ Lỗi đọc AppState:', error.message);
        rl.close();
    }
}

// Bắt đầu
askForCookie();

// Xử lý Ctrl+C
process.on('SIGINT', () => {
    console.log('\n👋 Đã hủy quá trình tạo AppState.');
    rl.close();
    process.exit(0);
});
