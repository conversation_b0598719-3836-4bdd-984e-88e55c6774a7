# 🤖 Messenger Chatbot

Chatbot Messenger đơn giản và hiệu quả, có thể đăng nhập Facebook, lắng nghe tin nhắn và phản hồi tự động.

## ✨ Tính năng

- 🔐 Đăng nhập an toàn bằng AppState
- 👂 Lắng nghe tin nhắn real-time
- 🤖 Phản hồi tự động thông minh
- 📋 Hệ thống lệnh đa dạng
- 🎨 Logger với màu sắc đẹp mắt
- ⚡ Hiệu suất cao và ổn định

## 🚀 Cài đặt

### 1. Clone hoặc tải về dự án

```bash
git clone <repository-url>
cd messenger-chatbot
```

### 2. Cài đặt dependencies

```bash
npm install
```

### 3. Cấu hình AppState

**Cách 1: Sử dụng Extension (Khuyến nghị)**
1. Cài đặt extension "Get cookies.txt" hoặc "Cookie Editor"
2. <PERSON><PERSON><PERSON> nhập Facebook trên trình duyệt
3. Xuất cookies dưới dạng JSON
4. Paste vào file `config/appstate.json`

**Cách 2: Th<PERSON> công**
1. Đ<PERSON><PERSON> nhập Facebook trên Chrome/Firefox
2. Mở Developer Tools (F12)
3. Vào tab Application > Storage > Cookies > facebook.com
4. Copy tất cả cookies và chuyển đổi sang định dạng JSON
5. Paste vào `config/appstate.json`

### 4. Chạy bot

```bash
npm start
```

hoặc để development:

```bash
npm run dev
```

## 📋 Danh sách lệnh

| Lệnh | Mô tả | Cách dùng |
|------|-------|-----------|
| `!ping` | Kiểm tra bot hoạt động | `!ping` |
| `!help` | Hiển thị danh sách lệnh | `!help` |
| `!time` | Hiển thị thời gian hiện tại | `!time` |
| `!info` | Thông tin về bot | `!info` |

## 🤖 Phản hồi tự động

Bot sẽ tự động phản hồi khi phát hiện:
- Lời chào: "hello", "hi", "chào"
- Lời cảm ơn: "cảm ơn", "thanks", "thank you"
- Lời tạm biệt: "bye", "tạm biệt", "goodbye"

## ⚙️ Cấu hình

Chỉnh sửa file `config/settings.json` để tùy chỉnh:

```json
{
  "bot": {
    "name": "DexBot",           // Tên bot
    "prefix": "!",              // Ký tự prefix cho lệnh
    "adminIds": [],             // ID admin (chưa sử dụng)
    "autoReply": true,          // Bật/tắt phản hồi tự động
    "logMessages": true         // Bật/tắt log tin nhắn
  }
}
```

## 🔧 Cấu trúc dự án

```
messenger-chatbot/
├── bot.js                 # File chính của bot
├── package.json           # Dependencies và scripts
├── README.md             # Hướng dẫn này
├── config/
│   ├── appstate.json     # Dữ liệu đăng nhập Facebook
│   └── settings.json     # Cấu hình bot
└── utils/
    └── logger.js         # Hệ thống log với màu sắc
```

## 🐛 Xử lý lỗi thường gặp

### Lỗi đăng nhập
- **AppState trống**: Kiểm tra file `config/appstate.json` có dữ liệu chưa
- **Checkpoint**: Đăng nhập Facebook trên trình duyệt để xử lý checkpoint
- **2FA**: Tắt xác thực 2 bước hoặc sử dụng app password

### Lỗi kết nối
- Kiểm tra kết nối internet
- Thử khởi động lại bot
- Cập nhật AppState mới

## 🔒 Bảo mật

- ⚠️ **KHÔNG** chia sẻ file `appstate.json` với ai
- 🔐 Sử dụng tài khoản phụ để chạy bot
- 🛡️ Thường xuyên cập nhật AppState
- 📝 Theo dõi log để phát hiện hoạt động bất thường

## 🚀 Mở rộng

### Thêm lệnh mới
1. Mở file `bot.js`
2. Thêm lệnh vào `setupCommands()`:
```javascript
this.commands.set('tenlenhmoí', this.tenLenhMoiCommand.bind(this));
```
3. Tạo function xử lý:
```javascript
tenLenhMoiCommand(threadID, messageID, senderID, args) {
    this.sendMessage(threadID, 'Phản hồi của lệnh mới', messageID);
}
```

### Thêm phản hồi tự động
Chỉnh sửa `handleAutoReply()` trong `bot.js` để thêm logic phản hồi mới.

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra log trong console
2. Đảm bảo AppState còn hợp lệ
3. Thử khởi động lại bot
4. Liên hệ developer để được hỗ trợ

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

---

**Lưu ý**: Bot này chỉ dành cho mục đích học tập và cá nhân. Hãy tuân thủ điều khoản sử dụng của Facebook.
