const chalk = require('chalk');
const moment = require('moment');

class Logger {
    constructor() {
        this.colors = {
            info: chalk.blue,
            success: chalk.green,
            warn: chalk.yellow,
            error: chalk.red,
            debug: chalk.gray
        };
    }

    getTimestamp() {
        return moment().format('YYYY-MM-DD HH:mm:ss');
    }

    formatMessage(level, message) {
        const timestamp = this.getTimestamp();
        const coloredLevel = this.colors[level](`[${level.toUpperCase()}]`);
        return `${chalk.gray(timestamp)} ${coloredLevel} ${message}`;
    }

    info(message) {
        console.log(this.formatMessage('info', message));
    }

    success(message) {
        console.log(this.formatMessage('success', message));
    }

    warn(message) {
        console.log(this.formatMessage('warn', message));
    }

    error(message) {
        console.log(this.formatMessage('error', message));
    }

    debug(message) {
        console.log(this.formatMessage('debug', message));
    }

    // Hiển thị banner khởi động
    showBanner() {
        console.log(chalk.cyan(`
╔══════════════════════════════════════╗
║        MESSENGER CHATBOT V1.0        ║
║          Đang khởi động...           ║
╚══════════════════════════════════════╝
        `));
    }

    // Hiển thị thông tin kết nối thành công
    showConnected(botName) {
        console.log(chalk.green(`
🎉 Bot đã kết nối thành công!
👤 Tên bot: ${botName}
⏰ Thời gian: ${this.getTimestamp()}
🚀 Bot đang hoạt động...
        `));
    }
}

module.exports = new Logger();
